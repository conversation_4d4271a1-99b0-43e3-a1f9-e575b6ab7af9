[project]
name = "infinity-sdk"
version = "0.6.0.dev5"
requires-python = ">=3.10"
dependencies = [
    "sqlglot~=11.7.0",
    "pydantic~=2.9.0",
    "thrift~=0.20.0",
    "setuptools~=75.2.0",
    "pytest~=8.3.0",
    "pandas~=2.2.0",
    "numpy~=1.26.0",
    "pyarrow~=17.0.0",
    "polars-lts-cpu~=1.9.0",
    "openpyxl~=3.1.0",
    "requests~=2.32.0",
    "readerwriterlock~=1.0.9"
]
description = "infinity"
readme = "README.md"

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
addopts = "--strict-markers"
markers = [
    "L0",
    "L1",
    "L2",
    "L3",
    "complex",
    "slow",
    "nightly",
]

filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::ResourceWarning",
    "ignore:pkg_resources is deprecated as an API:DeprecationWarning",
    # "ignore::thrift.transport.TTransport",
    # note the use of single quote below to denote "raw" strings in TOML
    'ignore:function ham\(\) is deprecated:DeprecationWarning',
]